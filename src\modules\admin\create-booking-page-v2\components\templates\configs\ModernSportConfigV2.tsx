'use client'

import { Settings } from 'lucide-react'
import React from 'react'
import {
  BasicInfoConfig,
  ContactInfoConfig,
  DescriptionLocationConfig,
  FieldsConfig,
  OperatingHoursConfig,

} from './shared'

const ModernSportConfig: React.FC = React.memo(() => {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Settings className="w-6 h-6 text-orange-500" />
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Cấu hình sân thể thao
          </h3>
          <p className="text-sm text-gray-600">
            Thiết lập thông tin cơ bản cho trang đặt sân
          </p>
        </div>
      </div>

      {/* Basic Information */}
      <BasicInfoConfig />

      {/* Operating Hours */}
      <OperatingHoursConfig />

      {/* Fields Configuration */}
      <FieldsConfig />

      {/* Description & Location */}
      <DescriptionLocationConfig />

      {/* Contact Information */}
      <ContactInfoConfig />
    </div>
  )
})

ModernSportConfig.displayName = 'ModernSportConfig'

export default ModernSportConfig
