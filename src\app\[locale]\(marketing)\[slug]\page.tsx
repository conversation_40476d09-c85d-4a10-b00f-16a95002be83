import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import { bookingAPIs } from '@/modules/booking/booking.apis'
import BookingPageNotAvailable from '@/modules/booking/components/BookingPageNotAvailable'
import PublicBookingPage from '@/modules/booking/components/PublicBookingPage'
import { PublicBookingPageV2 } from '@/modules/booking/components/PublicBookingPageV2'
import { getTranslations } from 'next-intl/server'

type Props = {
  params: {
    locale: string
    slug: string
  }
}

export async function generateMetadata({ params }: Props) {
  const { slug, locale } = await params
  try {
    // Fetch booking page data
    const response = await bookingAPIs.getBookingPageBySlug(slug)
    const bookingPage = response?.data as BookingPageItem

    return {
      title: bookingPage?.name,
      description: bookingPage?.description,
    }
  } catch (error: any) {
    // Fallback to default metadata if booking page not found
    console.error('Error fetching booking page:', error)
    const t = await getTranslations({
      locale,
      namespace: 'Index',
    })

    return {
      title: t('meta_title'),
      description: t('meta_description'),
    }
  }
}

export default async function BookingPageDetail({ params }: Props) {
  try {
    const { slug } = await params

    // Fetch booking page data
    const response = await bookingAPIs.getBookingPageBySlug(slug)

    if (response.status?.success && response?.data) {
      const bookingPage = response.data

      return <PublicBookingPageV2 bookingPage={bookingPage} />

      // Return the public booking page component with the data
      return <PublicBookingPage bookingPage={bookingPage} />
    } else {
      // Hiển thị trang "Booking Page Not Available" thay vì notFound()
      return <BookingPageNotAvailable slug={slug} />
    }
  } catch (error: any) {
    // Nếu có lỗi khi lấy dữ liệu, hiển thị trang "Booking Page Not Available"
    console.error('Error fetching booking page:', error?.message || error)
    const { slug } = params
    return <BookingPageNotAvailable slug={slug} />
  }
}
