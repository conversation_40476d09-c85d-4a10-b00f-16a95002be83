'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useCreateBookingPageV2Store } from '@/modules/admin/create-booking-page-v2/stores/create-booking-page-v2.store'
import { Clock } from 'lucide-react'
import React, { useCallback } from 'react'

export const OperatingHoursConfig: React.FC = React.memo(() => {
  // Use direct store access
  const config = useCreateBookingPageV2Store(state => state.bookingConfig)
  const updateBookingConfig = useCreateBookingPageV2Store(state => state.updateBookingConfig)

  const handleOpenTimeChange = useCallback((value: string) => {
    updateBookingConfig({ openTime: value })
  }, [updateBookingConfig])

  const handleCloseTimeChange = useCallback((value: string) => {
    updateBookingConfig({ closeTime: value })
  }, [updateBookingConfig])

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Clock className="w-4 h-4" />
          Giờ hoạt động
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="open-time">Giờ mở cửa</Label>
            <Input
              id="open-time"
              type="time"
              value={config.openTime}
              onChange={e => handleOpenTimeChange(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="close-time">Giờ đóng cửa</Label>
            <Input
              id="close-time"
              type="time"
              value={config.closeTime}
              onChange={e => handleCloseTimeChange(e.target.value)}
            />
          </div>
        </div>
        <p className="text-sm text-gray-600">
          Lịch đặt sân sẽ hiển thị các khung giờ từ
          {' '}
          {config.openTime}
          {' '}
          đến
          {' '}
          {config.closeTime}
        </p>
      </CardContent>
    </Card>
  )
})

OperatingHoursConfig.displayName = 'OperatingHoursConfig'
