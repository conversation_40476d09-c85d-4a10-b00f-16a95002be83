'use client'

import type { DescriptionSectionProps } from './types'
import { cn } from '@/lib/utils'
import { Clock, MapPin } from 'lucide-react'
import React from 'react'

const DescriptionSection: React.FC<DescriptionSectionProps> = ({
  config,
  pageInfo,
  previewMode = 'desktop',
  className,
  description,
  openTime,
  closeTime,
  location,
}) => {
  const isMobile = previewMode === 'mobile'

  return (
    <div className={cn(
      'bg-white rounded-lg border border-gray-200 p-6',
      className,
    )}
    >
      <div className="space-y-6">
        {/* Description Section */}
        <div className="space-y-4">
          <div>
            <h2 className={cn(
              'font-bold text-gray-900 mb-3',
              isMobile ? 'text-lg' : 'text-xl',
            )}
            >
              Thông tin sân
            </h2>
            <p className={cn(
              'text-gray-600 leading-relaxed',
              isMobile ? 'text-sm' : 'text-base',
            )}
            >
              {config.description || description || pageInfo.description || '<PERSON><PERSON> thể thao hiện đại với đầy đủ tiện nghi, phù hợp cho mọi lứa tuổi. Chúng tôi cam kết mang đến trải nghiệm tốt nhất cho khách hàng.'}
            </p>
          </div>

          {/* Info Items */}
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Clock className="w-5 h-5 text-orange-500 flex-shrink-0" />
              <span className={cn(
                'text-gray-700',
                isMobile ? 'text-sm' : 'text-base',
              )}
              >
                Giờ hoạt động:
                {' '}
                {openTime || config.openTime || '06:00'}
                {' '}
                -
                {' '}
                {closeTime || config.closeTime || '22:00'}
              </span>
            </div>

            <div className="flex items-center gap-3">
              <MapPin className="w-5 h-5 text-orange-500 flex-shrink-0" />
              <span className={cn(
                'text-gray-700',
                isMobile ? 'text-sm' : 'text-base',
              )}
              >
                {config.location || location || 'Sân thể thao ABC, Quận 1, TP.HCM'}
              </span>
            </div>

            {/* <div className="flex items-center gap-3">
              <Star className="w-5 h-5 text-orange-500 flex-shrink-0" />
              <span className={cn(
                'text-gray-700',
                isMobile ? 'text-sm' : 'text-base',
              )}
              >
                Đánh giá: 4.8/5 (128 đánh giá)
              </span>
            </div> */}
          </div>
        </div>

      </div>
    </div>
  )
}

export default DescriptionSection
