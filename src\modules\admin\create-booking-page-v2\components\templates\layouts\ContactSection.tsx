'use client'

import type { ContactSectionProps } from './types'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Facebook, Globe, Instagram, Mail, MapPin, Phone } from 'lucide-react'
import React from 'react'

const ContactSection: React.FC<ContactSectionProps> = ({
  config,
  previewMode = 'desktop',
  className,
  phone,
  email,
  address,
  socialLinks,
}) => {
  const isMobile = previewMode === 'mobile'

  // Use config values or fallback to props or defaults
  const contactPhone = config.contactInfo?.phone || phone || '0949 029 965'
  const contactEmail = config.contactInfo?.email || email || '<EMAIL>'
  const contactAddress = config.contactInfo?.address || address || 'Số 40, đường số 11, phường Trường Thọ, TP.Thủ <PERSON>, TP.HCM'
  const contactSocialLinks = {
    facebook: config.contactInfo?.socialLinks?.facebook || socialLinks?.facebook || 'https://facebook.com/booking-easy',
    instagram: config.contactInfo?.socialLinks?.instagram || socialLinks?.instagram || 'https://instagram.com/booking-easy',
    website: config.contactInfo?.socialLinks?.website || socialLinks?.website || 'https://booking-easy.com',
  }

  const contactItems = [
    {
      icon: Phone,
      label: 'Điện thoại',
      value: contactPhone,
      href: `tel:${contactPhone}`,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      icon: Mail,
      label: 'Email',
      value: contactEmail,
      href: `mailto:${contactEmail}`,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      icon: MapPin,
      label: 'Địa chỉ',
      value: contactAddress,
      href: `https://maps.google.com/?q=${encodeURIComponent(contactAddress)}`,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
    },
  ]

  const socialItems = [
    {
      icon: Facebook,
      label: 'Facebook',
      href: contactSocialLinks.facebook,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      icon: Instagram,
      label: 'Instagram',
      href: contactSocialLinks.instagram,
      color: 'text-pink-600',
      bgColor: 'bg-pink-100',
    },
    {
      icon: Globe,
      label: 'Website',
      href: contactSocialLinks.website,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
    },
  ].filter(item => item.href && item.href !== 'https://facebook.com/booking-easy' && item.href !== 'https://instagram.com/booking-easy' && item.href !== 'https://booking-easy.com')

  return (
    <Card className={cn('border-orange-200', className)}>
      <CardContent className="p-6">
        <h2 className={cn(
          'font-bold text-gray-900 mb-6',
          isMobile ? 'text-lg' : 'text-xl',
        )}
        >
          Thông tin liên hệ
        </h2>

        {/* Contact Information */}
        <div className="space-y-4">
          {contactItems.map((item, index) => (
            <div key={index} className="flex items-center gap-3 p-3 rounded-lg hover:bg-orange-50 transition-colors">
              <div className={cn(
                'p-2 rounded-lg flex-shrink-0',
                item.bgColor,
              )}
              >
                <item.icon className={cn('w-4 h-4', item.color)} />
              </div>
              <div className="flex-1 min-w-0">
                <div className={cn(
                  'font-medium text-gray-900',
                  isMobile ? 'text-sm' : 'text-base',
                )}
                >
                  {item.label}
                </div>
                <a
                  href={item.href}
                  target={item.label === 'Địa chỉ' ? '_blank' : undefined}
                  rel={item.label === 'Địa chỉ' ? 'noopener noreferrer' : undefined}
                  className={cn(
                    'text-gray-600 hover:text-orange-600 transition-colors break-words',
                    isMobile ? 'text-xs' : 'text-sm',
                  )}
                >
                  {item.value}
                </a>
              </div>
            </div>
          ))}
        </div>

        {/* Social Links */}
        {socialItems.length > 0 && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <h3 className={cn(
              'font-semibold text-gray-900 mb-4',
              isMobile ? 'text-base' : 'text-lg',
            )}
            >
              Mạng xã hội
            </h3>
            <div className="flex flex-wrap gap-3">
              {socialItems.map((item, index) => (
                <a
                  key={index}
                  href={item.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={cn(
                    'flex items-center gap-2 px-3 py-2 rounded-lg border border-gray-200 hover:border-orange-300 hover:bg-orange-50 transition-colors',
                    isMobile ? 'text-xs' : 'text-sm',
                  )}
                >
                  <item.icon className={cn('w-4 h-4', item.color)} />
                  <span className="font-medium">{item.label}</span>
                </a>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default ContactSection
