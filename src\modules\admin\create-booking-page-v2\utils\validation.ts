/* eslint-disable regexp/no-unused-capturing-group */
import { RESERVED_SUBDOMAINS } from '../constants/validation'

// Utility functions for specific validations
export const isValidSubdomain = (subdomain: string): boolean => {
  if (!subdomain || subdomain.length < 3 || subdomain.length > 50) {
    return false
  }
  if (!/^[a-z0-9-]+$/.test(subdomain)) {
    return false
  }
  if (subdomain.startsWith('-') || subdomain.endsWith('-')) {
    return false
  }
  if (subdomain.includes('--')) {
    return false
  }
  if (RESERVED_SUBDOMAINS.includes(subdomain.toLowerCase())) {
    return false
  }
  return true
}

export const isValidUrl = (url: string): boolean => {
  try {
    // eslint-disable-next-line no-new
    new URL(url)
    return true
  } catch {
    return false
  }
}

export const isValidTime = (time: string): boolean => {
  return /^([01]?\d|2[0-3]):[0-5]\d$/.test(time)
}

export const isValidImageUrl = (url: string): boolean => {
  if (!isValidUrl(url)) {
    return false
  }
  return /\.(jpg|jpeg|png|gif|webp)$/i.test(url)
}

// Format validation error messages for display
export const formatValidationErrors = (errors: Record<string, string>): string[] => {
  return Object.entries(errors)
    .filter(([_, message]) => message)
    .map(([field, message]) => `${getFieldDisplayName(field)}: ${message}`)
}

const getFieldDisplayName = (field: string): string => {
  const fieldNames: Record<string, string> = {
    subdomain: 'Tên miền phụ',
    customDomain: 'Tên miền tùy chỉnh',
    selectedTemplateId: 'Mẫu giao diện',
    bannerTitle: 'Tiêu đề banner',
    bannerSubtitle: 'Mô tả banner',
    bannerImage: 'Hình ảnh banner',
    openTime: 'Giờ mở cửa',
    closeTime: 'Giờ đóng cửa',
    fields: 'Danh sách sân',
  }

  return fieldNames[field] || field
}
