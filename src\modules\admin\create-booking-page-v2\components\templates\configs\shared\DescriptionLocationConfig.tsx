'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Settings } from 'lucide-react'
import React from 'react'
import { useCreateBookingPageV2Store } from '../../../../stores/create-booking-page-v2.store'

export const DescriptionLocationConfig: React.FC = React.memo(() => {
  // Use direct store access
  const config = useCreateBookingPageV2Store(state => state.bookingConfig)
  const updateBookingConfig = useCreateBookingPageV2Store(state => state.updateBookingConfig)
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Settings className="w-4 h-4" />
          <PERSON><PERSON> & <PERSON><PERSON><PERSON> điểm
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="description"><PERSON><PERSON> tả chi tiết</Label>
          <Textarea
            id="description"
            value={config.description}
            onChange={e => updateBookingConfig({ description: e.target.value })}
            placeholder="VD: Sân thể thao hiện đại với đầy đủ tiện nghi..."
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="location">Địa điểm</Label>
          <Input
            id="location"
            value={config.location}
            onChange={e => updateBookingConfig({ location: e.target.value })}
            placeholder="VD: Số 123, Đường ABC, Quận 1, TP.HCM"
          />
        </div>
      </CardContent>
    </Card>
  )
})

DescriptionLocationConfig.displayName = 'DescriptionLocationConfig'
