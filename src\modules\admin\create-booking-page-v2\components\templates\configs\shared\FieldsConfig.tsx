'use client'

import type { BookingField, FieldType } from '@/modules/admin/create-booking-page-v2/types'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { useCreateBookingPageV2Store } from '@/modules/admin/create-booking-page-v2/stores/create-booking-page-v2.store'
import { Plus, Trash2, Users } from 'lucide-react'
import React, { useCallback, useEffect, useState } from 'react'
import { useDebounce } from '../hooks/useDebounce'

const FIELD_TYPES: { value: FieldType, label: string, icon: string }[] = [
  { value: 'football', label: 'Bóng đá', icon: '⚽' },
  { value: 'tennis', label: 'Tennis', icon: '🎾' },
  { value: 'badminton', label: 'Cầu lông', icon: '🏸' },
  { value: 'basketball', label: 'Bóng rổ', icon: '🏀' },
]

// Memoized Field Item Component
const FieldItem = React.memo<{
  field: BookingField
  index: number
  onUpdate: (fieldId: string, updates: Partial<BookingField>) => void
  onRemove: (fieldId: string) => void
}>(({ field, onUpdate, onRemove }) => {
      const [localName, setLocalName] = useState(field.name)
      const debouncedName = useDebounce(localName, 300)

      useEffect(() => {
        if (debouncedName !== field.name) {
          onUpdate(field.id, { name: debouncedName })
        }
      }, [debouncedName, field.name, field.id, onUpdate])

      const handleTypeChange = useCallback((value: FieldType) => {
        onUpdate(field.id, { type: value })
      }, [field.id, onUpdate])

      const handleRemove = useCallback(() => {
        onRemove(field.id)
      }, [field.id, onRemove])

      return (
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="space-y-2">
              <Label>Tên sân</Label>
              <Input
                value={localName}
                onChange={e => setLocalName(e.target.value)}
                placeholder="VD: Sân VIP"
              />
            </div>

            <div className="space-y-2">
              <Label>Loại sân</Label>
              <Select
                value={field.type}
                onValueChange={handleTypeChange}
              >
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="w-full">
                  {FIELD_TYPES.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="flex items-center gap-2">
                        <span>{type.icon}</span>
                        <span>{type.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Hành động</Label>
              <div className="flex items-center justify-between mb-3">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleRemove}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )
    })

FieldItem.displayName = 'FieldItem'

export const FieldsConfig: React.FC = React.memo(() => {
  // Use direct store access
  const config = useCreateBookingPageV2Store(state => state.bookingConfig)
  const addField = useCreateBookingPageV2Store(state => state.addField)
  const removeField = useCreateBookingPageV2Store(state => state.removeField)
  const updateField = useCreateBookingPageV2Store(state => state.updateField)

  // Memoized handlers
  const handleFieldAdd = useCallback(() => {
    addField()
  }, [addField])

  const handleFieldRemove = useCallback((fieldId: string) => {
    removeField(fieldId)
  }, [removeField])

  const handleFieldUpdate = useCallback((fieldId: string, updates: Partial<BookingField>) => {
    updateField(fieldId, updates)
  }, [updateField])

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Users className="w-4 h-4" />
          Cấu hình sân (
          {config.fields.length}
          )
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {config.fields.length === 0
          ? (
              <div className="text-center py-8 text-gray-500">
                <Users className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p>Chưa có sân nào được thêm</p>
                <p className="text-sm">Nhấn "Thêm sân" để bắt đầu</p>
              </div>
            )
          : (
              <div className="space-y-3">
                {config.fields.map((field, index) => (
                  <FieldItem
                    key={field.id}
                    field={field}
                    index={index}
                    onUpdate={handleFieldUpdate}
                    onRemove={handleFieldRemove}
                  />
                ))}
              </div>
            )}

        <Separator />

        <Button
          type="button"
          variant="outline"
          onClick={handleFieldAdd}
          className="w-full border-dashed border-orange-300 text-orange-600 hover:bg-orange-50"
        >
          <Plus className="w-4 h-4 mr-2" />
          Thêm sân mới
        </Button>
      </CardContent>
    </Card>
  )
})

FieldsConfig.displayName = 'FieldsConfig'
