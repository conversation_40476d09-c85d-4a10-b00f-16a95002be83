'use client'

import type { CreateBookingPagePayload } from '../../apis/booking-page.api'

import { Button } from '@/components/ui/button'
import { appPaths } from '@/utils/app-routes'
import { Check, ChevronLeft, ChevronRight, FileText, Palette, Settings } from 'lucide-react'
import { useRouter } from 'next/navigation'
import React from 'react'
import { toast } from 'sonner'
import { bookingPageAPIs } from '../../apis/booking-page.api'
import { pageInfoSchema, templateSelectionSchema } from '../schemas/form-schemas'
import { useCreateBookingPageV2Store } from '../stores/create-booking-page-v2.store'

const STEPS = [
  { id: 1, title: 'Thông tin', icon: FileText },
  { id: 2, title: 'Chọn mẫu', icon: Palette },
  { id: 3, title: 'C<PERSON>u hình', icon: Settings },
]

const TOTAL_STEPS = 3

export const ProgressHeader: React.FC = () => {
  const { currentStep } = useCreateBookingPageV2Store()
  const router = useRouter()

  const {
    nextStep,
    prevStep,
    validateCurrentStep,
    pageInfo,
    selectedTemplateId,
    bookingConfig,
  } = useCreateBookingPageV2Store()

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return pageInfoSchema.safeParse(pageInfo)?.success
      case 2:
        return templateSelectionSchema.safeParse({ selectedTemplateId }).success
      case 3:
        return (
          bookingConfig.openTime !== ''
          && bookingConfig.closeTime !== ''
          && bookingConfig.fields.length > 0
        )
      default:
        return false
    }
  }

  const handleNext = () => {
    if (validateCurrentStep()) {
      nextStep()
    }
  }

  const handleFinish = async () => {
    if (!validateCurrentStep()) {
      return
    }
    try {
      // Có thể setIsLoading(true) nếu muốn loading state
      const payload: CreateBookingPagePayload = {
        name: pageInfo.name,
        description: pageInfo.description,
        slug: pageInfo.slug,
        templateCode: selectedTemplateId,
        blocks: [{
          type: 'config',
          data: {
            ...bookingConfig,
          },
        }], // Nếu có blocks thì lấy từ store, nếu không thì để []
        theme: {
          primaryColor: '#FF5722',
          fontFamily: 'Roboto',
          layout: 'default' as any,
        }, // Nếu có theme thì lấy từ store, nếu không thì để {}
        // ... các trường khác nếu cần
      }
      const response = await bookingPageAPIs.createBookingPage(payload)

      if (response?.status?.success) {
        toast.success('Tạo trang đặt chỗ thành công!')
        router.push(appPaths.admin.manageBookingPages()) // Đổi path nếu cần
      } else {
        toast.error(response?.status?.message)
      }
    } catch (err) {
      toast.error('Có lỗi khi tạo trang đặt chỗ')
    } finally {
      // setIsLoading(false) nếu có
    }
  }

  const renderBack = () => {
    return (
      <Button
        onClick={prevStep}
        disabled={currentStep === 1}
        variant="outline"
        size="lg"
        className="sm:w-auto border-orange-300 text-orange-600 hover:bg-orange-50 disabled:opacity-50 disabled:cursor-not-allowed h-12 px-6"
      >
        <ChevronLeft className="w-5 h-5 sm:mr-2" />
        <span className="hidden sm:block">Quay lại</span>
      </Button>
    )
  }

  const renderNext = () => {
    return (
      <>
        {/* Next/Finish Button */}
        {currentStep < TOTAL_STEPS
          ? (
              <Button
                onClick={handleNext}
                disabled={!canProceed()}
                size="lg"
                className="w-full sm:w-auto bg-orange-500 hover:bg-orange-600 text-white disabled:opacity-50 disabled:cursor-not-allowed h-12 px-6 shadow-lg"
              >
                <span className="hidden sm:block">Tiếp theo</span>
                <ChevronRight className="w-5 h-5 sm:ml-2" />
              </Button>
            )
          : (
              <Button
                onClick={handleFinish}
                disabled={!canProceed()}
                size="lg"
                className="sm:w-auto bg-green-500 hover:bg-green-600 text-white disabled:opacity-50 disabled:cursor-not-allowed h-12 px-6 shadow-lg"
              >
                <Check className="w-5 h-5 sm:mr-2" />
                <span className="hidden sm:block">Tạo trang đặt chỗ</span>
              </Button>
            )}
      </>
    )
  }

  return (
    <div className="bg-white/90 backdrop-blur-md border-b border-orange-200/50 sticky top-0 z-50 shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-2 lg:py-2">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* Title */}
          <div className="text-center lg:text-left">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">
              Tạo trang đặt chỗ
            </h1>
          </div>

          {/* Progress Steps - Desktop */}
          <div className="hidden lg:flex items-center space-x-4">
            {renderBack()}
            {STEPS.map((step, index) => {
              const Icon = step.icon
              const isActive = currentStep === step.id
              const isCompleted = currentStep > step.id

              return (
                <div key={step.id} className="flex items-center">
                  <div className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300
                    ${isActive
                  ? 'bg-orange-500 border-orange-500 text-white shadow-lg'
                  : isCompleted
                    ? 'bg-green-500 border-green-500 text-white'
                    : 'bg-white border-gray-300 text-gray-400'
                }
                  `}
                  >
                    {isCompleted
                      ? (
                          <Check className="w-4 h-4" />
                        )
                      : (
                          <Icon className="w-4 h-4" />
                        )}
                  </div>
                  <div className="ml-3">
                    <div className={`text-sm font-medium ${
                      isActive ? 'text-orange-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                    }`}
                    >
                      Bước
                      {' '}
                      {step.id}
                    </div>
                    <div className={`text-xs ${
                      isActive ? 'text-orange-500' : isCompleted ? 'text-green-500' : 'text-gray-400'
                    }`}
                    >
                      {step.title}
                    </div>
                  </div>
                  {index < STEPS.length - 1 && (
                    <div className={`w-8 h-0.5 mx-4 ${isCompleted ? 'bg-green-300' : 'bg-gray-200'}`} />
                  )}
                </div>
              )
            })}
            {renderNext()}
          </div>

          {/* Progress Steps - Mobile */}
          <div className="flex flex-row items-center gap-2 lg:hidden justify-center">
            {renderBack()}
            <div className="flex items-center space-x-2">
              {STEPS.map((step, index) => {
                const isActive = currentStep === step.id
                const isCompleted = currentStep > step.id

                return (
                  <div key={step.id} className="flex items-center">
                    <div className={`
                      w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium transition-all duration-300
                      ${isActive
                    ? 'bg-orange-500 text-white shadow-lg'
                    : isCompleted
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-200 text-gray-500'
                  }
                    `}
                    >
                      {isCompleted ? <Check className="w-4 h-4" /> : step.id}
                    </div>
                    {index < STEPS.length - 1 && (
                      <div className={`w-4 h-0.5 mx-1 ${isCompleted ? 'bg-green-300' : 'bg-gray-200'}`} />
                    )}
                  </div>
                )
              })}
            </div>
            {renderNext()}
          </div>
        </div>
      </div>
    </div>
  )
}
