// This file previously contained optimized hooks for the create-booking-page-v2 module.
// All hooks have been removed as components now use direct store access for better performance and simplicity.
//
// Instead of using these hooks, components now directly access the store like:
// const pageInfo = useCreateBookingPageV2Store(state => state.pageInfo)
// const updatePageInfo = useCreateBookingPageV2Store(state => state.updatePageInfo)
//
// This approach eliminates unnecessary abstractions and provides better performance
// by allowing Zustand to optimize re-renders at the selector level.
